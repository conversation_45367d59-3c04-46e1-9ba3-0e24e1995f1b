<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API Connection Test</h1>
        <p>This tool tests the connection between frontend and backend to diagnose query issues.</p>

        <div class="test-section">
            <h3>1. Backend Health Check</h3>
            <p>Test if the backend server is running and responding.</p>
            <button onclick="testHealth()">Test Health Endpoint</button>
            <div id="health-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. Query Stream Endpoint Test</h3>
            <p>Test the actual query/stream endpoint that the frontend uses.</p>
            <button onclick="testQueryStream()">Test Query Stream</button>
            <div id="stream-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. CORS Test</h3>
            <p>Test if CORS is properly configured for frontend-backend communication.</p>
            <button onclick="testCORS()">Test CORS</button>
            <div id="cors-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. Full Integration Test</h3>
            <p>Test the complete flow that the frontend uses.</p>
            <button onclick="testFullFlow()">Test Full Flow</button>
            <div id="full-result" class="result"></div>
        </div>
    </div>

    <script>
        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.textContent = 'Testing...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch('http://localhost:8000/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = '✅ SUCCESS: Backend server is healthy!\n\n' + JSON.stringify(data, null, 2);
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = '❌ ERROR: Health check failed\n\n' + JSON.stringify(data, null, 2);
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = '❌ NETWORK ERROR: ' + error.message + '\n\nBackend server may not be running. Please start it with:\npython main.py\n\nOr check if it\'s running on a different port.';
                resultDiv.className = 'result error';
            }
        }

        async function testQueryStream() {
            const resultDiv = document.getElementById('stream-result');
            resultDiv.textContent = 'Testing query stream endpoint...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch('http://localhost:8000/query/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: 'test query for connection verification',
                        output_format: 'json'
                    })
                });

                console.log('Stream Response:', {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries())
                });

                if (response.ok) {
                    const text = await response.text();
                    resultDiv.textContent = '✅ SUCCESS: Query stream endpoint is working!\n\nResponse:\n' + text;
                    resultDiv.className = 'result success';
                } else {
                    const text = await response.text();
                    resultDiv.textContent = '❌ ERROR: Query stream failed\n\nStatus: ' + response.status + '\nResponse:\n' + text;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = '❌ NETWORK ERROR: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testCORS() {
            const resultDiv = document.getElementById('cors-result');
            resultDiv.textContent = 'Testing CORS configuration...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch('http://localhost:8000/health', {
                    method: 'OPTIONS'
                });

                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };

                resultDiv.textContent = '✅ CORS Headers:\n\n' + JSON.stringify(corsHeaders, null, 2);
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = '❌ CORS ERROR: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testFullFlow() {
            const resultDiv = document.getElementById('full-result');
            resultDiv.textContent = 'Testing full integration flow...';
            resultDiv.className = 'result info';

            try {
                // Step 1: Health check
                const healthResponse = await fetch('http://localhost:8000/health');
                if (!healthResponse.ok) {
                    throw new Error('Health check failed');
                }

                // Step 2: Query stream test
                const queryResponse = await fetch('http://localhost:8000/query/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: 'show me employee data',
                        output_format: 'excel'
                    })
                });

                if (queryResponse.ok) {
                    const result = await queryResponse.text();
                    resultDiv.textContent = '✅ SUCCESS: Full flow working!\n\nThe frontend should now be able to:\n1. Send queries to backend ✓\n2. Receive responses ✓\n3. Display waiting messages ✓\n\nSample response:\n' + result.substring(0, 500) + '...';
                    resultDiv.className = 'result success';
                } else {
                    throw new Error('Query failed with status: ' + queryResponse.status);
                }
            } catch (error) {
                resultDiv.textContent = '❌ FULL FLOW ERROR: ' + error.message;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
